"""Main entry point for the multi-agent system."""

import asyncio
import sys
from typing import Optional
from agents import Runner
from domain_agents.orchestrator import create_orchestrator_agent, get_all_agents
from memory_ops.mem0_client import mem0_client
from config.settings import settings


async def setup_litellm_model():
    """Setup LiteLLM model configuration for Gemini API."""
    try:
        # For now, we'll use a mock setup since the OpenAI Agents framework
        # requires additional configuration for custom models
        import os

        # Set environment variables for potential future use
        os.environ["GEMINI_API_KEY"] = settings.GEMINI_API_KEY

        print(f"Environment configured for Gemini API")
        print("Note: This demo uses mock responses since Gemini integration requires additional setup")
        return True
    except Exception as e:
        print(f"Error setting up environment: {e}")
        return False


async def test_memory_operations():
    """Test Mem0 MCP memory operations."""
    print("\n=== Testing Mem0 MCP Memory Operations ===")
    
    try:
        # Connect to Mem0 MCP server
        await mem0_client.connect()
        
        if not await mem0_client.is_connected():
            print("❌ Failed to connect to Mem0 MCP server")
            print(f"Server URL: {mem0_client.server_url}")
            print("Please ensure the MCP server is running and accessible")
            return False
        
        # Run comprehensive memory tests
        test_results = await mem0_client.test_memory_operations()
        
        print("✅ Memory Operations Test Results:")
        print(f"Connection Status: {'✅ Connected' if test_results['connection_test'] else '❌ Disconnected'}")
        print(f"Available Tools: {len(test_results['available_tools'])} tools found")
        
        for tool in test_results['available_tools']:
            print(f"  - {tool['name']}: {tool['description']}")
        
        print("\nOperation Results:")
        for op_name, result in test_results['operations'].items():
            status = "✅ Success" if result.get('success') else "❌ Failed"
            print(f"  {op_name}: {status}")
            if not result.get('success'):
                print(f"    Error: {result.get('error', 'Unknown error')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Memory operations test failed: {e}")
        return False
    finally:
        await mem0_client.disconnect()


async def run_agent_demo():
    """Run a demonstration of the multi-agent system."""
    print("\n=== Multi-Agent System Demo ===")

    # Setup model
    model_ready = await setup_litellm_model()
    if not model_ready:
        print("❌ Model setup failed. Please check your Gemini API configuration.")
        return

    # Create agents to show their structure
    agents = get_all_agents()

    print("✅ Multi-Agent System Structure:")
    print(f"Total agents created: {len(agents)}")

    for name, agent in agents.items():
        print(f"\n🤖 {name.upper()}: {agent.name}")
        if hasattr(agent, 'instructions'):
            print(f"   Instructions: {agent.instructions[:100]}...")
        if hasattr(agent, 'tools') and agent.tools:
            print(f"   Tools available: {len(agent.tools)}")
            for tool in agent.tools:
                print(f"     - {tool.__name__}")
        if hasattr(agent, 'handoffs') and agent.handoffs:
            print(f"   Can handoff to: {[a.name for a in agent.handoffs]}")

    print("\n=== Testing Individual Agent Tools ===")

    # Test healthcare agent tools
    healthcare_agent = agents["healthcare"]
    print(f"\n🏥 Testing {healthcare_agent.name} tools:")

    # Test BMI calculation
    for tool in healthcare_agent.tools:
        if tool.__name__ == "calculate_bmi":
            result = tool(70, 1.75)
            print(f"   BMI calculation (70kg, 1.75m): {result}")
            break

    # Test AI research agent tools
    ai_agent = agents["ai_research"]
    print(f"\n🧠 Testing {ai_agent.name} tools:")

    for tool in ai_agent.tools:
        if tool.__name__ == "get_research_trends":
            trends = tool()
            print(f"   Current AI trends: {trends[:3]}...")  # Show first 3
            break

    # Test admin agent tools
    admin_agent = agents["admin"]
    print(f"\n📋 Testing {admin_agent.name} tools:")

    for tool in admin_agent.tools:
        if tool.__name__ == "calculate_budget":
            items = [{"name": "Item 1", "amount": 100}, {"name": "Item 2", "amount": 50}]
            budget = tool(items, 0.1)
            print(f"   Budget calculation: {budget}")
            break

    print("\n✅ Agent system structure and tools are working correctly!")
    print("Note: Full LLM integration requires OpenAI API key or custom model configuration.")


async def interactive_mode():
    """Run the system in interactive mode."""
    print("\n=== Interactive Multi-Agent System ===")
    print("Type your questions and the system will route them to the appropriate specialist.")
    print("Type 'quit' to exit, 'help' for available commands.\n")
    
    # Setup model
    model_ready = await setup_litellm_model()
    if not model_ready:
        print("❌ Model setup failed. Please check your Gemini API configuration.")
        return
    
    orchestrator = create_orchestrator_agent()
    
    while True:
        try:
            user_input = input("You: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("Goodbye!")
                break
            elif user_input.lower() == 'help':
                print("\nAvailable commands:")
                print("- Ask any question about healthcare, AI research, or administrative tasks")
                print("- 'test memory' - Test memory operations")
                print("- 'agents' - List available agents")
                print("- 'quit' - Exit the system")
                continue
            elif user_input.lower() == 'test memory':
                await test_memory_operations()
                continue
            elif user_input.lower() == 'agents':
                agents = get_all_agents()
                print("\nAvailable agents:")
                for name, agent in agents.items():
                    print(f"- {name}: {agent.name}")
                continue
            elif not user_input:
                continue
            
            print("Processing your request...")
            result = await Runner.run(orchestrator, user_input)
            print(f"\nAgent: {result.final_output}\n")
            
        except KeyboardInterrupt:
            print("\nGoodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}\n")


async def main():
    """Main function to run the multi-agent system."""
    print("🤖 Multi-Agent System with OpenAI SDK Agents Framework")
    print("=" * 60)
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "demo":
            await run_agent_demo()
        elif command == "memory":
            await test_memory_operations()
        elif command == "interactive":
            await interactive_mode()
        else:
            print(f"Unknown command: {command}")
            print("Available commands: demo, memory, interactive")
    else:
        # Default to interactive mode
        await interactive_mode()


if __name__ == "__main__":
    asyncio.run(main())
