"""Mem0 MCP client for memory operations."""

import async<PERSON>
from typing import Dict, Any, List, Optional
from agents.mcp.server import MCPServerSse, MCPServerSseParams
from config.settings import settings


class Mem0MCPClient:
    """Client for interacting with Mem0 MCP server."""
    
    def __init__(self, server_url: Optional[str] = None):
        """Initialize Mem0 MCP client."""
        self.server_url = server_url or settings.mem0_mcp_url
        self.server = None
        self._connected = False
    
    async def connect(self) -> bool:
        """Connect to the Mem0 MCP server."""
        try:
            params = MCPServerSseParams(url=self.server_url)
            self.server = MCPServerSse(
                params=params,
                cache_tools_list=True
            )
            
            # Test connection by listing tools
            await self.server.list_tools()
            self._connected = True
            print(f"Successfully connected to Mem0 MCP server at {self.server_url}")
            return True
            
        except Exception as e:
            print(f"Failed to connect to Mem0 MCP server: {e}")
            self._connected = False
            return False
    
    async def disconnect(self):
        """Disconnect from the MCP server."""
        if self.server:
            # MCPServerSse doesn't have a close method, just set to None
            self.server = None
            self._connected = False
    
    async def is_connected(self) -> bool:
        """Check if connected to the server."""
        return self._connected
    
    async def list_available_tools(self) -> List[Dict[str, Any]]:
        """List all available tools from the MCP server."""
        if not self._connected:
            raise ConnectionError("Not connected to MCP server")
        
        try:
            tools = await self.server.list_tools()
            return tools
        except Exception as e:
            print(f"Error listing tools: {e}")
            return []
    
    async def add_memory(self, content: str, metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Add a new memory to Mem0."""
        if not self._connected:
            raise ConnectionError("Not connected to MCP server")
        
        try:
            params = {
                "content": content,
                "metadata": metadata or {}
            }
            
            result = await self.server.call_tool("add_memory", params)
            return result
            
        except Exception as e:
            print(f"Error adding memory: {e}")
            return {"error": str(e)}
    
    async def retrieve_memory(self, query: str, limit: int = 10) -> Dict[str, Any]:
        """Retrieve memories based on query."""
        if not self._connected:
            raise ConnectionError("Not connected to MCP server")
        
        try:
            params = {
                "query": query,
                "limit": limit
            }
            
            result = await self.server.call_tool("retrieve_memory", params)
            return result
            
        except Exception as e:
            print(f"Error retrieving memory: {e}")
            return {"error": str(e)}
    
    async def store_memory(self, memory_id: str, content: str, metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Store or update a specific memory."""
        if not self._connected:
            raise ConnectionError("Not connected to MCP server")
        
        try:
            params = {
                "memory_id": memory_id,
                "content": content,
                "metadata": metadata or {}
            }
            
            result = await self.server.call_tool("store_memory", params)
            return result
            
        except Exception as e:
            print(f"Error storing memory: {e}")
            return {"error": str(e)}
    
    async def delete_memory(self, memory_id: str) -> Dict[str, Any]:
        """Delete a specific memory."""
        if not self._connected:
            raise ConnectionError("Not connected to MCP server")
        
        try:
            params = {
                "memory_id": memory_id
            }
            
            result = await self.server.call_tool("delete_memory", params)
            return result
            
        except Exception as e:
            print(f"Error deleting memory: {e}")
            return {"error": str(e)}
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.disconnect()
