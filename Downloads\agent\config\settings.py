"""Configuration settings for the multi-agent system."""

import os
from typing import Dict, Any
from pydantic import BaseSettings, Field


class Settings(BaseSettings):
    """Application settings."""
    
    # Gemini API Configuration
    gemini_api_key: str = Field(
        default="AIzaSyBZWuoZTnFC5oNjbEV9BaBnodtEt3C7_Ns",
        description="Gemini API key"
    )
    gemini_model: str = Field(
        default="gemini-1.5-flash",
        description="Gemini model to use"
    )
    
    # MCP Configuration
    mem0_mcp_url: str = Field(
        default="http://localhost:8765/mcp/openmemory/sse/Usama_Fresh",
        description="Mem0 MCP server URL"
    )
    
    # Agent Configuration
    max_iterations: int = Field(default=10, description="Maximum agent iterations")
    temperature: float = Field(default=0.7, description="Model temperature")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


def get_litellm_config() -> Dict[str, Any]:
    """Get LiteLLM configuration for Gemini."""
    settings = Settings()
    
    return {
        "model": f"gemini/{settings.gemini_model}",
        "api_key": settings.gemini_api_key,
        "temperature": settings.temperature,
        "max_tokens": 4096,
    }


def get_agent_model_settings() -> Dict[str, Any]:
    """Get model settings for OpenAI Agents SDK."""
    settings = Settings()
    
    return {
        "model": f"gemini/{settings.gemini_model}",
        "temperature": settings.temperature,
        "max_tokens": 4096,
    }


# Global settings instance
settings = Settings()
