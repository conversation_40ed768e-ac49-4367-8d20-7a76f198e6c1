[project]
name = "multi-agent-system"
version = "0.1.0"
description = "Multi-agent system using OpenAI SDK Agents framework with Gemini API"
authors = [
    {name = "Agent System", email = "<EMAIL>"}
]
dependencies = [
    "openai-agents>=0.0.17",
    "google-generativeai>=0.8.0",
    "litellm>=1.0.0",
    "pydantic>=2.0.0",
    "httpx>=0.27.0",
    "python-dotenv>=1.0.0",
]
requires-python = ">=3.9"
readme = "README.md"
license = {text = "MIT"}

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["domain_agents", "config", "memory_ops"]

[tool.uv]
dev-dependencies = [
    "pytest>=8.0.0",
    "pytest-asyncio>=0.23.0",
    "black>=24.0.0",
    "isort>=5.13.0",
]

[tool.black]
line-length = 88
target-version = ['py39']

[tool.isort]
profile = "black"
line_length = 88
