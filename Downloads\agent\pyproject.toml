[project]
name = "multi-agent-system"
version = "0.1.0"
description = "Multi-agent system with specialized domain agents and Mem0 MCP integration"
authors = [
    {name = "Agent System", email = "<EMAIL>"}
]
dependencies = [
    "openai-agents>=0.1.0",
    "litellm>=1.0.0",
    "pydantic>=2.0.0",
    "asyncio-mqtt>=0.16.0",
    "httpx>=0.25.0",
    "python-dotenv>=1.0.0",
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
]
requires-python = ">=3.9"
readme = "README.md"
license = {text = "MIT"}

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.pytest.ini_options]
asyncio_mode = "auto"
testpaths = ["tests"]

[tool.ruff]
line-length = 88
target-version = "py39"

[tool.ruff.lint]
select = ["E", "F", "I", "N", "W"]
ignore = ["E501"]

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
