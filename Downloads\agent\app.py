"""Main application for testing the multi-agent system."""

import asyncio
from agents import Runner

from healthcare_agent import create_healthcare_agent
from ai_research_agent import create_ai_research_agent
from admin_agent import create_admin_agent


async def test_healthcare_agent():
    """Test the healthcare agent."""
    print("🏥 Testing Healthcare Agent")
    print("-" * 40)
    
    agent = create_healthcare_agent()
    task = "Explain the key HIPAA compliance requirements for patient data storage and transmission."
    
    print(f"Task: {task}")
    print("Processing...")
    
    result = await Runner.run(agent, task)
    print(f"Response: {result.final_output}")
    print()


async def test_ai_research_agent():
    """Test the AI research agent."""
    print("🤖 Testing AI Research Agent")
    print("-" * 40)
    
    agent = create_ai_research_agent()
    task = "Compare the architectural differences between BERT and GPT transformer models."
    
    print(f"Task: {task}")
    print("Processing...")
    
    result = await Runner.run(agent, task)
    print(f"Response: {result.final_output}")
    print()


async def test_admin_agent():
    """Test the administrative agent."""
    print("📋 Testing Administrative Agent")
    print("-" * 40)
    
    agent = create_admin_agent()
    task = "Create a 6-month project timeline for implementing a new customer relationship management system."
    
    print(f"Task: {task}")
    print("Processing...")
    
    result = await Runner.run(agent, task)
    print(f"Response: {result.final_output}")
    print()


async def main():
    """Run all agent tests."""
    print("🚀 Multi-Agent System Test")
    print("=" * 50)
    print("Testing three specialized agents using OpenAI SDK Agents framework")
    print("=" * 50)
    print()
    
    try:
        # Test each agent
        await test_healthcare_agent()
        await test_ai_research_agent()
        await test_admin_agent()
        
        print("✅ All agents tested successfully!")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
